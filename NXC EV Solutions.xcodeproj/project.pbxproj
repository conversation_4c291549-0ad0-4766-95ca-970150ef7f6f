// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		7A413981DD2C2F2B48853D12 /* Pods_NXC_EV_Solutions.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5A0F6A87C29692D71556753F /* Pods_NXC_EV_Solutions.framework */; };
		8B162F4526CFB500000D20E2 /* PaymentDetailsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B162F4426CFB500000D20E2 /* PaymentDetailsVC.swift */; };
		8B162F4726CFB526000D20E2 /* PaymentDetailsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B162F4626CFB526000D20E2 /* PaymentDetailsCell.swift */; };
		8B22E6342746521F008FB06B /* ConnectionLostVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B22E6332746521F008FB06B /* ConnectionLostVC.swift */; };
		8B287C1E26F46EF2006CAAE9 /* ScanQRVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B287C1D26F46EF2006CAAE9 /* ScanQRVC.swift */; };
		8B2D578C2713DAE4007224D6 /* TermsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B2D578B2713DAE4007224D6 /* TermsVC.swift */; };
		8B389B9826B3F565002DCBA6 /* FilterCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B389B9726B3F565002DCBA6 /* FilterCell.swift */; };
		8B3FFCF826D36FD700858D86 /* ConnectorCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B3FFCF726D36FD700858D86 /* ConnectorCell.swift */; };
		8B3FFCFA26D395D200858D86 /* ConnectorDetailsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B3FFCF926D395D200858D86 /* ConnectorDetailsVC.swift */; };
		8B3FFCFC26D3967300858D86 /* ConnectorDetailsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B3FFCFB26D3967300858D86 /* ConnectorDetailsCell.swift */; };
		8B3FFD0126D4C06E00858D86 /* OrderCardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B3FFD0026D4C06E00858D86 /* OrderCardVC.swift */; };
		8B3FFD0326D4C08800858D86 /* ViewCardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B3FFD0226D4C08800858D86 /* ViewCardVC.swift */; };
		8B427BA626A0491E00921A6D /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427BA526A0491E00921A6D /* AppDelegate.swift */; };
		8B427C0C26A04F8500921A6D /* AppStoryboards.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427C0B26A04F8500921A6D /* AppStoryboards.swift */; };
		8B427C1826A13C8600921A6D /* SplashVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427C1726A13C8600921A6D /* SplashVC.swift */; };
		8B427C1B26A13C9900921A6D /* LoginVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427C1A26A13C9900921A6D /* LoginVC.swift */; };
		8B427CEA26A1831D00921A6D /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427C6E26A1831C00921A6D /* Constants.swift */; };
		8B427D5326A1831D00921A6D /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427CE926A1831D00921A6D /* Extensions.swift */; };
		8B427DA526A194E100921A6D /* VerificationVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427DA426A194E100921A6D /* VerificationVC.swift */; };
		8B427DAC26A19E7700921A6D /* UserInfoVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427DAB26A19E7700921A6D /* UserInfoVC.swift */; };
		8B427DDA26A6CD8500921A6D /* VehicleVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B427DD926A6CD8500921A6D /* VehicleVC.swift */; };
		8B4400C2271432EF00126FD7 /* SOTabBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B4400BA271432EE00126FD7 /* SOTabBar.swift */; };
		8B4400C3271432EF00126FD7 /* SOTabBarSetting.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B4400B8271432ED00126FD7 /* SOTabBarSetting.swift */; };
		8B4400C4271432EF00126FD7 /* SOTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B4400B9271432EE00126FD7 /* SOTabBarController.swift */; };
		8B4400C5271432EF00126FD7 /* SOTabBarItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B4400B7271432EC00126FD7 /* SOTabBarItem.swift */; };
		8B4400C92714431200126FD7 /* MenuVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B4400C82714431200126FD7 /* MenuVC.swift */; };
		8B50F6F826DE24D10067759A /* AFWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B50F6F726DE24D10067759A /* AFWrapper.swift */; };
		8B50F6FA26DF3AD80067759A /* VehicleModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B50F6F926DF3AD80067759A /* VehicleModel.swift */; };
		8B548F8426E77F2700EB7964 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B548F8326E77F2700EB7964 /* Main.storyboard */; };
		8B548F8626E77F3100EB7964 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B548F8526E77F3100EB7964 /* LaunchScreen.storyboard */; };
		8B5EA6C326FDBC6C00033C87 /* ReplaceCardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B5EA6C226FDBC6C00033C87 /* ReplaceCardVC.swift */; };
		8B5F75B0272AA48B00839D98 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8B5F75AF272AA48B00839D98 /* GoogleService-Info.plist */; };
		8B5F75B6272BF05500839D98 /* OfferCouponCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B5F75B5272BF05500839D98 /* OfferCouponCell.swift */; };
		8B5F75B8272C02B400839D98 /* TermsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B5F75B7272C02B300839D98 /* TermsCell.swift */; };
		8B5F75BD272F9B1400839D98 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 8B5F75BF272F9B1400839D98 /* Localizable.strings */; };
		8B5F75C4272F9BA700839D98 /* PreLogin.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B5F75C6272F9BA700839D98 /* PreLogin.storyboard */; };
		8B6874D626CA37C400830A1F /* AddMoneyVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6874D526CA37C400830A1F /* AddMoneyVC.swift */; };
		8B6874D926CBC91300830A1F /* AddComplainVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6874D826CBC91300830A1F /* AddComplainVC.swift */; };
		8B6874DB26CBC93300830A1F /* ComplainListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6874DA26CBC93300830A1F /* ComplainListVC.swift */; };
		8B6874DD26CBC95500830A1F /* ComplainCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6874DC26CBC95500830A1F /* ComplainCell.swift */; };
		8B6874E226CBD31200830A1F /* LanguageVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6874E126CBD31200830A1F /* LanguageVC.swift */; };
		8B68752A26CE1B2400830A1F /* MenuCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B68752926CE1B2400830A1F /* MenuCell.swift */; };
		8B68752C26CF88CA00830A1F /* ProfileDetailsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B68752B26CF88C800830A1F /* ProfileDetailsCell.swift */; };
		8B68752E26CF88E300830A1F /* ProfileDetailsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B68752D26CF88E300830A1F /* ProfileDetailsVC.swift */; };
		8B6BEB9C26BCFB570033611B /* WalletCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6BEB9B26BCFB570033611B /* WalletCell.swift */; };
		8B6BEB9E26BD34D20033611B /* HistoryVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B6BEB9D26BD34D20033611B /* HistoryVC.swift */; };
		8B79AFD326AC14EC00A71D86 /* ChargingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B79AFD226AC14EC00A71D86 /* ChargingVC.swift */; };
		8B79AFD626AC169A00A71D86 /* WalletVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B79AFD526AC169A00A71D86 /* WalletVC.swift */; };
		8B7B1D1727356C2D002D4094 /* Charging.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D1927356C2D002D4094 /* Charging.storyboard */; };
		8B7B1D1C27356C54002D4094 /* Complain.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D1E27356C54002D4094 /* Complain.storyboard */; };
		8B7B1D2127356C5F002D4094 /* Language.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D2327356C5F002D4094 /* Language.storyboard */; };
		8B7B1D2627356C6D002D4094 /* RFID.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D2827356C6D002D4094 /* RFID.storyboard */; };
		8B7B1D2B27356C76002D4094 /* Help.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D2D27356C76002D4094 /* Help.storyboard */; };
		8B7B1D3027356CBD002D4094 /* Charger.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D3227356CBD002D4094 /* Charger.storyboard */; };
		8B7B1D3527356CD6002D4094 /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D3727356CD6002D4094 /* Home.storyboard */; };
		8B7B1D3A27356CE1002D4094 /* Vehicle.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D3C27356CE1002D4094 /* Vehicle.storyboard */; };
		8B7B1D3F27356CEE002D4094 /* Profile.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8B7B1D4127356CEE002D4094 /* Profile.storyboard */; };
		8B7F396E2742592700621296 /* BuyChargerVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B7F396D2742592700621296 /* BuyChargerVC.swift */; };
		8B80C794270330B800450242 /* TransactionDetailsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B80C793270330B800450242 /* TransactionDetailsVC.swift */; };
		8B82761E26EB12E6002ED58C /* ic_mapMarker.svg in Resources */ = {isa = PBXBuildFile; fileRef = 8B82761D26EB12E5002ED58C /* ic_mapMarker.svg */; };
		8B82762126EB144E002ED58C /* ic_marker_map.png in Resources */ = {isa = PBXBuildFile; fileRef = 8B82762026EB144D002ED58C /* ic_marker_map.png */; };
		8B84F3E0271FD590008C8DA9 /* TrackRFIDStatusCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B84F3DF271FD590008C8DA9 /* TrackRFIDStatusCell.swift */; };
		8B84F3E227218336008C8DA9 /* NewsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B84F3E127218336008C8DA9 /* NewsVC.swift */; };
		8BB8C86F26FF1451007D5BC1 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C86E26FF1451007D5BC1 /* Configuration.swift */; };
		8BB8C87826FF24B8007D5BC1 /* LBXScanNetAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87026FF24B3007D5BC1 /* LBXScanNetAnimation.swift */; };
		8BB8C87926FF24B8007D5BC1 /* LBXScanLineAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87126FF24B4007D5BC1 /* LBXScanLineAnimation.swift */; };
		8BB8C87A26FF24B8007D5BC1 /* LBXScanViewStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87226FF24B5007D5BC1 /* LBXScanViewStyle.swift */; };
		8BB8C87B26FF24B8007D5BC1 /* LBXScanView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87326FF24B6007D5BC1 /* LBXScanView.swift */; };
		8BB8C87C26FF24B8007D5BC1 /* LBXScanViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87426FF24B6007D5BC1 /* LBXScanViewController.swift */; };
		8BB8C87D26FF24B8007D5BC1 /* LBXPermissions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87526FF24B7007D5BC1 /* LBXPermissions.swift */; };
		8BB8C87E26FF24B8007D5BC1 /* LBXScanWrapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87626FF24B8007D5BC1 /* LBXScanWrapper.swift */; };
		8BB8C87F26FF24B8007D5BC1 /* BundleExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BB8C87726FF24B8007D5BC1 /* BundleExtension.swift */; };
		8BBA48B226D617D1008554A4 /* UIColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48AA26D617CE008554A4 /* UIColor.swift */; };
		8BBA48B326D617D1008554A4 /* UIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48AB26D617CF008554A4 /* UIImage.swift */; };
		8BBA48B426D617D1008554A4 /* ATCClassicWalkthroughViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 8BBA48AC26D617CF008554A4 /* ATCClassicWalkthroughViewController.xib */; };
		8BBA48B526D617D1008554A4 /* UIViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48AD26D617D0008554A4 /* UIViewController.swift */; };
		8BBA48B626D617D1008554A4 /* ATCWalkthroughModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48AE26D617D0008554A4 /* ATCWalkthroughModel.swift */; };
		8BBA48B726D617D1008554A4 /* ATCWalkthroughViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 8BBA48AF26D617D0008554A4 /* ATCWalkthroughViewController.xib */; };
		8BBA48B826D617D1008554A4 /* ATCWalkthroughViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48B026D617D0008554A4 /* ATCWalkthroughViewController.swift */; };
		8BBA48B926D617D1008554A4 /* ATCClassicWalkthroughViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48B126D617D1008554A4 /* ATCClassicWalkthroughViewController.swift */; };
		8BBA48BE26D61ACD008554A4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8BBA48BD26D61ACD008554A4 /* Assets.xcassets */; };
		8BBA48C026D61B78008554A4 /* OnboardingVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48BF26D61B78008554A4 /* OnboardingVC.swift */; };
		8BBA48C226D8AFFF008554A4 /* AddressVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48C126D8AFFF008554A4 /* AddressVC.swift */; };
		8BBA48C426D8BF35008554A4 /* ProfileModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBA48C326D8BF35008554A4 /* ProfileModel.swift */; };
		8BBCE5552716A62700B8B68C /* ConnectorTimeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BBCE5542716A62700B8B68C /* ConnectorTimeCell.swift */; };
		8BC054DF2735447D00D8EC24 /* Wallet.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8BC054E12735447D00D8EC24 /* Wallet.storyboard */; };
		8BC7D7A126C3D57B004D1B8A /* Roboto-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79526C3D56B004D1B8A /* Roboto-Thin.ttf */; };
		8BC7D7A226C3D57B004D1B8A /* Roboto-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79626C3D56C004D1B8A /* Roboto-ThinItalic.ttf */; };
		8BC7D7A326C3D57B004D1B8A /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79726C3D56D004D1B8A /* Roboto-Bold.ttf */; };
		8BC7D7A426C3D57B004D1B8A /* Roboto-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79826C3D56F004D1B8A /* Roboto-LightItalic.ttf */; };
		8BC7D7A526C3D57B004D1B8A /* Roboto-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79926C3D570004D1B8A /* Roboto-BoldItalic.ttf */; };
		8BC7D7A626C3D57B004D1B8A /* Roboto-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79A26C3D571004D1B8A /* Roboto-MediumItalic.ttf */; };
		8BC7D7A726C3D57B004D1B8A /* Roboto-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79B26C3D573004D1B8A /* Roboto-BlackItalic.ttf */; };
		8BC7D7A826C3D57B004D1B8A /* Roboto-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79C26C3D574004D1B8A /* Roboto-Black.ttf */; };
		8BC7D7A926C3D57B004D1B8A /* Roboto-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79D26C3D576004D1B8A /* Roboto-Light.ttf */; };
		8BC7D7AA26C3D57B004D1B8A /* Roboto-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79E26C3D577004D1B8A /* Roboto-Italic.ttf */; };
		8BC7D7AB26C3D57B004D1B8A /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D79F26C3D579004D1B8A /* Roboto-Regular.ttf */; };
		8BC7D7AC26C3D57B004D1B8A /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8BC7D7A026C3D57B004D1B8A /* Roboto-Medium.ttf */; };
		8BCE825F270433240023D2EF /* TransactionModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BCE825E270433240023D2EF /* TransactionModel.swift */; };
		8BCE82612704341A0023D2EF /* TransactionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BCE82602704341A0023D2EF /* TransactionCell.swift */; };
		8BCE8263270467B10023D2EF /* HelpVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BCE8262270467B10023D2EF /* HelpVC.swift */; };
		8BCE8267270467E10023D2EF /* AboutUsVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BCE8266270467E10023D2EF /* AboutUsVC.swift */; };
		8BD18C6E26E0E58900CABF3F /* WalletModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BD18C6D26E0E58900CABF3F /* WalletModel.swift */; };
		8BD18C7026E1D69A00CABF3F /* ComplainModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BD18C6F26E1D69A00CABF3F /* ComplainModel.swift */; };
		8BE077A926E9CDC400D63131 /* ChargeStationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BE077A826E9CDC300D63131 /* ChargeStationModel.swift */; };
		8BEA651326FC7B7200131053 /* RFIDTrackListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEA651226FC7B7200131053 /* RFIDTrackListVC.swift */; };
		8BEA651526FC7BD900131053 /* RFIDTrackListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEA651426FC7BD900131053 /* RFIDTrackListCell.swift */; };
		8BEA651726FC7FE100131053 /* RFIDModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEA651626FC7FE100131053 /* RFIDModel.swift */; };
		8BEA651926FC89A000131053 /* TrackRFIDStatusVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEA651826FC89A000131053 /* TrackRFIDStatusVC.swift */; };
		8BEA651B26FC912600131053 /* IssueNewCardVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEA651A26FC912600131053 /* IssueNewCardVC.swift */; };
		8BEB3CAD26A91C26000BEA73 /* HomeTabVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEB3CAC26A91C26000BEA73 /* HomeTabVC.swift */; };
		8BEB3D2B26A94FDB000BEA73 /* MapChargerVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEB3D2A26A94FDB000BEA73 /* MapChargerVC.swift */; };
		8BEB3D2E26A94FE9000BEA73 /* ListChargerVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEB3D2D26A94FE9000BEA73 /* ListChargerVC.swift */; };
		8BEB3D3426A972A4000BEA73 /* mapStyle.json in Resources */ = {isa = PBXBuildFile; fileRef = 8BEB3D3326A972A4000BEA73 /* mapStyle.json */; };
		8BEB3D3726A98844000BEA73 /* FilterVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BEB3D3626A98844000BEA73 /* FilterVC.swift */; };
		8BF0295826F054230026B21E /* ChargeStationSearchCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BF0295726F054230026B21E /* ChargeStationSearchCell.swift */; };
		8BFB24BA27116963002EE4B0 /* ic_map_marker.png in Resources */ = {isa = PBXBuildFile; fileRef = 8BFB24B927116963002EE4B0 /* ic_map_marker.png */; };
		8BFB24BE27119B1C002EE4B0 /* GameListVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24BD27119B1C002EE4B0 /* GameListVC.swift */; };
		8BFB24C027119B33002EE4B0 /* GameListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24BF27119B33002EE4B0 /* GameListCell.swift */; };
		8BFB24C227119B47002EE4B0 /* GameDetailVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24C127119B47002EE4B0 /* GameDetailVC.swift */; };
		8BFB24C427119F39002EE4B0 /* GameModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24C327119F39002EE4B0 /* GameModel.swift */; };
		8BFB24C72711B16C002EE4B0 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24C52711B16B002EE4B0 /* ViewController.swift */; };
		8BFB24C82711B16C002EE4B0 /* RearViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB24C62711B16C002EE4B0 /* RearViewController.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		5A0F6A87C29692D71556753F /* Pods_NXC_EV_Solutions.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NXC_EV_Solutions.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		649DAB7331AB254FC54FA0F5 /* Pods-NXC EV Solutions.debug - live.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NXC EV Solutions.debug - live.xcconfig"; path = "Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions.debug - live.xcconfig"; sourceTree = "<group>"; };
		7B470C53F29B44A42153B57F /* Pods-NXC EV Solutions.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NXC EV Solutions.release.xcconfig"; path = "Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions.release.xcconfig"; sourceTree = "<group>"; };
		8B162F4426CFB500000D20E2 /* PaymentDetailsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentDetailsVC.swift; sourceTree = "<group>"; };
		8B162F4626CFB526000D20E2 /* PaymentDetailsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentDetailsCell.swift; sourceTree = "<group>"; };
		8B22E6332746521F008FB06B /* ConnectionLostVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectionLostVC.swift; sourceTree = "<group>"; };
		8B287C1D26F46EF2006CAAE9 /* ScanQRVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScanQRVC.swift; sourceTree = "<group>"; };
		8B294602273A59500047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/PreLogin.strings; sourceTree = "<group>"; };
		8B294603273A59500047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Profile.strings; sourceTree = "<group>"; };
		8B294604273A59510047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Vehicle.strings; sourceTree = "<group>"; };
		8B294605273A59520047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Home.strings; sourceTree = "<group>"; };
		8B294606273A59530047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Charger.strings; sourceTree = "<group>"; };
		8B294607273A59530047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Charging.strings; sourceTree = "<group>"; };
		8B294608273A59540047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Wallet.strings; sourceTree = "<group>"; };
		8B294609273A59810047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Complain.strings; sourceTree = "<group>"; };
		8B29460A273A59820047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Language.strings; sourceTree = "<group>"; };
		8B29460B273A59830047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/RFID.strings; sourceTree = "<group>"; };
		8B29460C273A59830047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Help.strings; sourceTree = "<group>"; };
		8B29460D273A59830047FF6D /* hi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = hi; path = hi.lproj/Localizable.strings; sourceTree = "<group>"; };
		8B2D578B2713DAE4007224D6 /* TermsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TermsVC.swift; sourceTree = "<group>"; };
		8B389B9726B3F565002DCBA6 /* FilterCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterCell.swift; sourceTree = "<group>"; };
		8B3FFCF726D36FD700858D86 /* ConnectorCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectorCell.swift; sourceTree = "<group>"; };
		8B3FFCF926D395D200858D86 /* ConnectorDetailsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectorDetailsVC.swift; sourceTree = "<group>"; };
		8B3FFCFB26D3967300858D86 /* ConnectorDetailsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectorDetailsCell.swift; sourceTree = "<group>"; };
		8B3FFD0026D4C06E00858D86 /* OrderCardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrderCardVC.swift; sourceTree = "<group>"; };
		8B3FFD0226D4C08800858D86 /* ViewCardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewCardVC.swift; sourceTree = "<group>"; };
		8B427BA226A0491E00921A6D /* NXC EV Solutions.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "NXC EV Solutions.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		8B427BA526A0491E00921A6D /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		8B427BB326A0492100921A6D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8B427C0B26A04F8500921A6D /* AppStoryboards.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppStoryboards.swift; sourceTree = "<group>"; };
		8B427C1726A13C8600921A6D /* SplashVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashVC.swift; sourceTree = "<group>"; };
		8B427C1A26A13C9900921A6D /* LoginVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginVC.swift; sourceTree = "<group>"; };
		8B427C6E26A1831C00921A6D /* Constants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		8B427CE926A1831D00921A6D /* Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		8B427DA426A194E100921A6D /* VerificationVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerificationVC.swift; sourceTree = "<group>"; };
		8B427DAB26A19E7700921A6D /* UserInfoVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoVC.swift; sourceTree = "<group>"; };
		8B427DD926A6CD8500921A6D /* VehicleVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VehicleVC.swift; sourceTree = "<group>"; };
		8B4400B7271432EC00126FD7 /* SOTabBarItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SOTabBarItem.swift; sourceTree = "<group>"; };
		8B4400B8271432ED00126FD7 /* SOTabBarSetting.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SOTabBarSetting.swift; sourceTree = "<group>"; };
		8B4400B9271432EE00126FD7 /* SOTabBarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SOTabBarController.swift; sourceTree = "<group>"; };
		8B4400BA271432EE00126FD7 /* SOTabBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SOTabBar.swift; sourceTree = "<group>"; };
		8B4400BB271432EF00126FD7 /* SOTabBar.modulemap */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.module-map"; path = SOTabBar.modulemap; sourceTree = "<group>"; };
		8B4400BC271432EF00126FD7 /* SOTabBar-dummy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SOTabBar-dummy.m"; sourceTree = "<group>"; };
		8B4400BD271432EF00126FD7 /* SOTabBar-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "SOTabBar-Info.plist"; sourceTree = "<group>"; };
		8B4400BE271432EF00126FD7 /* SOTabBar-prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SOTabBar-prefix.pch"; sourceTree = "<group>"; };
		8B4400BF271432EF00126FD7 /* SOTabBar-umbrella.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SOTabBar-umbrella.h"; sourceTree = "<group>"; };
		8B4400C0271432EF00126FD7 /* SOTabBar.release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = SOTabBar.release.xcconfig; sourceTree = "<group>"; };
		8B4400C82714431200126FD7 /* MenuVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuVC.swift; sourceTree = "<group>"; };
		8B4D8356273A95F5009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/PreLogin.strings; sourceTree = "<group>"; };
		8B4D8357273A95F7009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Profile.strings; sourceTree = "<group>"; };
		8B4D8358273A95F7009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Vehicle.strings; sourceTree = "<group>"; };
		8B4D8359273A95F8009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Home.strings; sourceTree = "<group>"; };
		8B4D835A273A95F9009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Charger.strings; sourceTree = "<group>"; };
		8B4D835B273A95FA009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Charging.strings; sourceTree = "<group>"; };
		8B4D835C273A95FA009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Wallet.strings; sourceTree = "<group>"; };
		8B4D835D273A962D009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Complain.strings; sourceTree = "<group>"; };
		8B4D835E273A962D009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Language.strings; sourceTree = "<group>"; };
		8B4D835F273A962E009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/RFID.strings; sourceTree = "<group>"; };
		8B4D8360273A962E009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Help.strings; sourceTree = "<group>"; };
		8B4D8361273A962F009C0CA9 /* gu */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = gu; path = gu.lproj/Localizable.strings; sourceTree = "<group>"; };
		8B50F6F726DE24D10067759A /* AFWrapper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AFWrapper.swift; path = "NXC EV Solutions/View Controllers/AFWrapper.swift"; sourceTree = SOURCE_ROOT; };
		8B50F6F926DF3AD80067759A /* VehicleModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VehicleModel.swift; sourceTree = "<group>"; };
		8B548F8326E77F2700EB7964 /* Main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Main.storyboard; sourceTree = "<group>"; };
		8B548F8526E77F3100EB7964 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		8B5EA6C226FDBC6C00033C87 /* ReplaceCardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReplaceCardVC.swift; sourceTree = "<group>"; };
		8B5F75AE272AA1E100839D98 /* NXC EV SolutionsRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "NXC EV SolutionsRelease.entitlements"; sourceTree = "<group>"; };
		8B5F75AF272AA48B00839D98 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		8B5F75B1272AA75300839D98 /* NXC EV Solutions.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "NXC EV Solutions.entitlements"; sourceTree = "<group>"; };
		8B5F75B2272AA76300839D98 /* NXC EV SolutionsDebug - Local.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "NXC EV SolutionsDebug - Local.entitlements"; sourceTree = "<group>"; };
		8B5F75B5272BF05500839D98 /* OfferCouponCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OfferCouponCell.swift; sourceTree = "<group>"; };
		8B5F75B7272C02B300839D98 /* TermsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TermsCell.swift; sourceTree = "<group>"; };
		8B5F75C1272F9B1F00839D98 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		8B6874D526CA37C400830A1F /* AddMoneyVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddMoneyVC.swift; sourceTree = "<group>"; };
		8B6874D826CBC91300830A1F /* AddComplainVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddComplainVC.swift; sourceTree = "<group>"; };
		8B6874DA26CBC93300830A1F /* ComplainListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComplainListVC.swift; sourceTree = "<group>"; };
		8B6874DC26CBC95500830A1F /* ComplainCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComplainCell.swift; sourceTree = "<group>"; };
		8B6874E126CBD31200830A1F /* LanguageVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageVC.swift; sourceTree = "<group>"; };
		8B68752926CE1B2400830A1F /* MenuCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuCell.swift; sourceTree = "<group>"; };
		8B68752B26CF88C800830A1F /* ProfileDetailsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileDetailsCell.swift; sourceTree = "<group>"; };
		8B68752D26CF88E300830A1F /* ProfileDetailsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileDetailsVC.swift; sourceTree = "<group>"; };
		8B6BEB9B26BCFB570033611B /* WalletCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletCell.swift; sourceTree = "<group>"; };
		8B6BEB9D26BD34D20033611B /* HistoryVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryVC.swift; sourceTree = "<group>"; };
		8B79AFD226AC14EC00A71D86 /* ChargingVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChargingVC.swift; sourceTree = "<group>"; };
		8B79AFD526AC169A00A71D86 /* WalletVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletVC.swift; sourceTree = "<group>"; };
		8B7B1D1827356C2D002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Charging.storyboard; sourceTree = "<group>"; };
		8B7B1D1D27356C54002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Complain.storyboard; sourceTree = "<group>"; };
		8B7B1D2227356C5F002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Language.storyboard; sourceTree = "<group>"; };
		8B7B1D2727356C6D002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/RFID.storyboard; sourceTree = "<group>"; };
		8B7B1D2C27356C76002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Help.storyboard; sourceTree = "<group>"; };
		8B7B1D3127356CBD002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Charger.storyboard; sourceTree = "<group>"; };
		8B7B1D3627356CD6002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Home.storyboard; sourceTree = "<group>"; };
		8B7B1D3B27356CE1002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Vehicle.storyboard; sourceTree = "<group>"; };
		8B7B1D4027356CEE002D4094 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Profile.storyboard; sourceTree = "<group>"; };
		8B7F396D2742592700621296 /* BuyChargerVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BuyChargerVC.swift; sourceTree = "<group>"; };
		8B80C793270330B800450242 /* TransactionDetailsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionDetailsVC.swift; sourceTree = "<group>"; };
		8B82761D26EB12E5002ED58C /* ic_mapMarker.svg */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = ic_mapMarker.svg; sourceTree = "<group>"; };
		8B82762026EB144D002ED58C /* ic_marker_map.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = ic_marker_map.png; sourceTree = "<group>"; };
		8B84F3DF271FD590008C8DA9 /* TrackRFIDStatusCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackRFIDStatusCell.swift; sourceTree = "<group>"; };
		8B84F3E127218336008C8DA9 /* NewsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewsVC.swift; sourceTree = "<group>"; };
		8BB8C86E26FF1451007D5BC1 /* Configuration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Configuration.swift; sourceTree = "<group>"; };
		8BB8C87026FF24B3007D5BC1 /* LBXScanNetAnimation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanNetAnimation.swift; sourceTree = "<group>"; };
		8BB8C87126FF24B4007D5BC1 /* LBXScanLineAnimation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanLineAnimation.swift; sourceTree = "<group>"; };
		8BB8C87226FF24B5007D5BC1 /* LBXScanViewStyle.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanViewStyle.swift; sourceTree = "<group>"; };
		8BB8C87326FF24B6007D5BC1 /* LBXScanView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanView.swift; sourceTree = "<group>"; };
		8BB8C87426FF24B6007D5BC1 /* LBXScanViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanViewController.swift; sourceTree = "<group>"; };
		8BB8C87526FF24B7007D5BC1 /* LBXPermissions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXPermissions.swift; sourceTree = "<group>"; };
		8BB8C87626FF24B8007D5BC1 /* LBXScanWrapper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LBXScanWrapper.swift; sourceTree = "<group>"; };
		8BB8C87726FF24B8007D5BC1 /* BundleExtension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BundleExtension.swift; sourceTree = "<group>"; };
		8BBA48AA26D617CE008554A4 /* UIColor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIColor.swift; sourceTree = "<group>"; };
		8BBA48AB26D617CF008554A4 /* UIImage.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIImage.swift; sourceTree = "<group>"; };
		8BBA48AC26D617CF008554A4 /* ATCClassicWalkthroughViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ATCClassicWalkthroughViewController.xib; sourceTree = "<group>"; };
		8BBA48AD26D617D0008554A4 /* UIViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIViewController.swift; sourceTree = "<group>"; };
		8BBA48AE26D617D0008554A4 /* ATCWalkthroughModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ATCWalkthroughModel.swift; sourceTree = "<group>"; };
		8BBA48AF26D617D0008554A4 /* ATCWalkthroughViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ATCWalkthroughViewController.xib; sourceTree = "<group>"; };
		8BBA48B026D617D0008554A4 /* ATCWalkthroughViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ATCWalkthroughViewController.swift; sourceTree = "<group>"; };
		8BBA48B126D617D1008554A4 /* ATCClassicWalkthroughViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ATCClassicWalkthroughViewController.swift; sourceTree = "<group>"; };
		8BBA48BD26D61ACD008554A4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Assets.xcassets; path = "NXC EV Solutions/Assets.xcassets"; sourceTree = SOURCE_ROOT; };
		8BBA48BF26D61B78008554A4 /* OnboardingVC.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OnboardingVC.swift; sourceTree = "<group>"; };
		8BBA48C126D8AFFF008554A4 /* AddressVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddressVC.swift; sourceTree = "<group>"; };
		8BBA48C326D8BF35008554A4 /* ProfileModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileModel.swift; sourceTree = "<group>"; };
		8BBCE5542716A62700B8B68C /* ConnectorTimeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectorTimeCell.swift; sourceTree = "<group>"; };
		8BC054E02735447D00D8EC24 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Wallet.storyboard; sourceTree = "<group>"; };
		8BC7D79526C3D56B004D1B8A /* Roboto-Thin.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Thin.ttf"; sourceTree = "<group>"; };
		8BC7D79626C3D56C004D1B8A /* Roboto-ThinItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-ThinItalic.ttf"; sourceTree = "<group>"; };
		8BC7D79726C3D56D004D1B8A /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Bold.ttf"; sourceTree = "<group>"; };
		8BC7D79826C3D56F004D1B8A /* Roboto-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-LightItalic.ttf"; sourceTree = "<group>"; };
		8BC7D79926C3D570004D1B8A /* Roboto-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-BoldItalic.ttf"; sourceTree = "<group>"; };
		8BC7D79A26C3D571004D1B8A /* Roboto-MediumItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-MediumItalic.ttf"; sourceTree = "<group>"; };
		8BC7D79B26C3D573004D1B8A /* Roboto-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-BlackItalic.ttf"; sourceTree = "<group>"; };
		8BC7D79C26C3D574004D1B8A /* Roboto-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Black.ttf"; sourceTree = "<group>"; };
		8BC7D79D26C3D576004D1B8A /* Roboto-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Light.ttf"; sourceTree = "<group>"; };
		8BC7D79E26C3D577004D1B8A /* Roboto-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Italic.ttf"; sourceTree = "<group>"; };
		8BC7D79F26C3D579004D1B8A /* Roboto-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Regular.ttf"; sourceTree = "<group>"; };
		8BC7D7A026C3D57B004D1B8A /* Roboto-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Medium.ttf"; sourceTree = "<group>"; };
		8BC8999A27350CFE00151B02 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/PreLogin.storyboard; sourceTree = "<group>"; };
		8BCE825E270433240023D2EF /* TransactionModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionModel.swift; sourceTree = "<group>"; };
		8BCE82602704341A0023D2EF /* TransactionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionCell.swift; sourceTree = "<group>"; };
		8BCE8262270467B10023D2EF /* HelpVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelpVC.swift; sourceTree = "<group>"; };
		8BCE8266270467E10023D2EF /* AboutUsVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsVC.swift; sourceTree = "<group>"; };
		8BD18C6D26E0E58900CABF3F /* WalletModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletModel.swift; sourceTree = "<group>"; };
		8BD18C6F26E1D69A00CABF3F /* ComplainModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComplainModel.swift; sourceTree = "<group>"; };
		8BE077A826E9CDC300D63131 /* ChargeStationModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChargeStationModel.swift; sourceTree = "<group>"; };
		8BEA651226FC7B7200131053 /* RFIDTrackListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RFIDTrackListVC.swift; sourceTree = "<group>"; };
		8BEA651426FC7BD900131053 /* RFIDTrackListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RFIDTrackListCell.swift; sourceTree = "<group>"; };
		8BEA651626FC7FE100131053 /* RFIDModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RFIDModel.swift; sourceTree = "<group>"; };
		8BEA651826FC89A000131053 /* TrackRFIDStatusVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackRFIDStatusVC.swift; sourceTree = "<group>"; };
		8BEA651A26FC912600131053 /* IssueNewCardVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IssueNewCardVC.swift; sourceTree = "<group>"; };
		8BEB3CAC26A91C26000BEA73 /* HomeTabVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeTabVC.swift; sourceTree = "<group>"; };
		8BEB3D2A26A94FDB000BEA73 /* MapChargerVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapChargerVC.swift; sourceTree = "<group>"; };
		8BEB3D2D26A94FE9000BEA73 /* ListChargerVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ListChargerVC.swift; sourceTree = "<group>"; };
		8BEB3D3326A972A4000BEA73 /* mapStyle.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = mapStyle.json; sourceTree = "<group>"; };
		8BEB3D3626A98844000BEA73 /* FilterVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterVC.swift; sourceTree = "<group>"; };
		8BF0295726F054230026B21E /* ChargeStationSearchCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChargeStationSearchCell.swift; sourceTree = "<group>"; };
		8BFB24B927116963002EE4B0 /* ic_map_marker.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = ic_map_marker.png; sourceTree = "<group>"; };
		8BFB24BD27119B1C002EE4B0 /* GameListVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameListVC.swift; sourceTree = "<group>"; };
		8BFB24BF27119B33002EE4B0 /* GameListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameListCell.swift; sourceTree = "<group>"; };
		8BFB24C127119B47002EE4B0 /* GameDetailVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameDetailVC.swift; sourceTree = "<group>"; };
		8BFB24C327119F39002EE4B0 /* GameModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameModel.swift; sourceTree = "<group>"; };
		8BFB24C52711B16B002EE4B0 /* ViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		8BFB24C62711B16C002EE4B0 /* RearViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RearViewController.swift; sourceTree = "<group>"; };
		D3B3DAC8297597BA646000B1 /* Pods-NXC EV Solutions.debug - local.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NXC EV Solutions.debug - local.xcconfig"; path = "Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions.debug - local.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8B427B9F26A0491E00921A6D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7A413981DD2C2F2B48853D12 /* Pods_NXC_EV_Solutions.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		52F09DB2AABC965EE647DC19 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5A0F6A87C29692D71556753F /* Pods_NXC_EV_Solutions.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8B3FFCFD26D4C00A00858D86 /* RFID */ = {
			isa = PBXGroup;
			children = (
				8BEA651626FC7FE100131053 /* RFIDModel.swift */,
				8B3FFD0026D4C06E00858D86 /* OrderCardVC.swift */,
				8BEA651226FC7B7200131053 /* RFIDTrackListVC.swift */,
				8BEA651426FC7BD900131053 /* RFIDTrackListCell.swift */,
				8BEA651826FC89A000131053 /* TrackRFIDStatusVC.swift */,
				8B84F3DF271FD590008C8DA9 /* TrackRFIDStatusCell.swift */,
				8BEA651A26FC912600131053 /* IssueNewCardVC.swift */,
				8B5EA6C226FDBC6C00033C87 /* ReplaceCardVC.swift */,
				8B3FFD0226D4C08800858D86 /* ViewCardVC.swift */,
				8B7B1D2827356C6D002D4094 /* RFID.storyboard */,
			);
			name = RFID;
			sourceTree = "<group>";
		};
		8B427B9926A0491E00921A6D = {
			isa = PBXGroup;
			children = (
				8B427BA426A0491E00921A6D /* NXC EV Solutions */,
				8B427BA326A0491E00921A6D /* Products */,
				E8C72C8D8D220CB2D247389B /* Pods */,
				52F09DB2AABC965EE647DC19 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8B427BA326A0491E00921A6D /* Products */ = {
			isa = PBXGroup;
			children = (
				8B427BA226A0491E00921A6D /* NXC EV Solutions.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8B427BA426A0491E00921A6D /* NXC EV Solutions */ = {
			isa = PBXGroup;
			children = (
				8B5F75B2272AA76300839D98 /* NXC EV SolutionsDebug - Local.entitlements */,
				8B5F75B1272AA75300839D98 /* NXC EV Solutions.entitlements */,
				8B5F75AE272AA1E100839D98 /* NXC EV SolutionsRelease.entitlements */,
				8BEB3D3326A972A4000BEA73 /* mapStyle.json */,
				8BB8C86E26FF1451007D5BC1 /* Configuration.swift */,
				8BB8C87726FF24B8007D5BC1 /* BundleExtension.swift */,
				8B4400C62714330200126FD7 /* LBX */,
				8B4400C72714331500126FD7 /* SOTab */,
				8B82761F26EB12EB002ED58C /* Icons */,
				8B427C6C26A1831C00921A6D /* Utils */,
				8BF1C7B426DE24610092358D /* Service */,
				8B427BE726A04F5C00921A6D /* Application Delegate */,
				8B427C0E26A04F9E00921A6D /* View Controllers */,
				8B5F75BF272F9B1400839D98 /* Localizable.strings */,
				8B427BE426A04F0E00921A6D /* Storyboards */,
				8B427BB326A0492100921A6D /* Info.plist */,
				8B5F75AF272AA48B00839D98 /* GoogleService-Info.plist */,
				8BBA48BD26D61ACD008554A4 /* Assets.xcassets */,
			);
			path = "NXC EV Solutions";
			sourceTree = "<group>";
		};
		8B427BE426A04F0E00921A6D /* Storyboards */ = {
			isa = PBXGroup;
			children = (
				8B548F8326E77F2700EB7964 /* Main.storyboard */,
				8B548F8526E77F3100EB7964 /* LaunchScreen.storyboard */,
			);
			path = Storyboards;
			sourceTree = "<group>";
		};
		8B427BE726A04F5C00921A6D /* Application Delegate */ = {
			isa = PBXGroup;
			children = (
				8B427BA526A0491E00921A6D /* AppDelegate.swift */,
				8B427C0B26A04F8500921A6D /* AppStoryboards.swift */,
			);
			path = "Application Delegate";
			sourceTree = "<group>";
		};
		8B427C0E26A04F9E00921A6D /* View Controllers */ = {
			isa = PBXGroup;
			children = (
				8B427C1D26A13CFE00921A6D /* PreLogin */,
				8B427DA726A19E0D00921A6D /* Profile */,
				8B427DD826A6CD4C00921A6D /* Vehicle */,
				8BEB3CA326A7E007000BEA73 /* Home */,
				8BEB3D2926A94FBE000BEA73 /* Charger */,
				8B79AFD126AC14D000A71D86 /* Charging */,
				8B79AFD426AC168600A71D86 /* Wallet */,
				8B6874D726CBC8FA00830A1F /* Complain */,
				8B6874E026CBD2C600830A1F /* Language */,
				8B3FFCFD26D4C00A00858D86 /* RFID */,
				8BCE82682704684F0023D2EF /* About Us */,
			);
			path = "View Controllers";
			sourceTree = "<group>";
		};
		8B427C1D26A13CFE00921A6D /* PreLogin */ = {
			isa = PBXGroup;
			children = (
				8BFB24C62711B16C002EE4B0 /* RearViewController.swift */,
				8BFB24C52711B16B002EE4B0 /* ViewController.swift */,
				8B427C1726A13C8600921A6D /* SplashVC.swift */,
				8BBA48BF26D61B78008554A4 /* OnboardingVC.swift */,
				8B427C1A26A13C9900921A6D /* LoginVC.swift */,
				8B427DA426A194E100921A6D /* VerificationVC.swift */,
				8B4400C82714431200126FD7 /* MenuVC.swift */,
				8B5F75C6272F9BA700839D98 /* PreLogin.storyboard */,
			);
			name = PreLogin;
			sourceTree = "<group>";
		};
		8B427C6C26A1831C00921A6D /* Utils */ = {
			isa = PBXGroup;
			children = (
				8BBA48BA26D617D8008554A4 /* Onboarding */,
				8B79AFD926AC177600A71D86 /* Fonts */,
				8B427C6D26A1831C00921A6D /* Constants */,
				8B427CE826A1831D00921A6D /* Extensions */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		8B427C6D26A1831C00921A6D /* Constants */ = {
			isa = PBXGroup;
			children = (
				8B427C6E26A1831C00921A6D /* Constants.swift */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		8B427CE826A1831D00921A6D /* Extensions */ = {
			isa = PBXGroup;
			children = (
				8B427CE926A1831D00921A6D /* Extensions.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		8B427DA726A19E0D00921A6D /* Profile */ = {
			isa = PBXGroup;
			children = (
				8BBA48C326D8BF35008554A4 /* ProfileModel.swift */,
				8B427DAB26A19E7700921A6D /* UserInfoVC.swift */,
				8BBA48C126D8AFFF008554A4 /* AddressVC.swift */,
				8B68752D26CF88E300830A1F /* ProfileDetailsVC.swift */,
				8B68752B26CF88C800830A1F /* ProfileDetailsCell.swift */,
				8B7B1D4127356CEE002D4094 /* Profile.storyboard */,
			);
			name = Profile;
			sourceTree = "<group>";
		};
		8B427DD826A6CD4C00921A6D /* Vehicle */ = {
			isa = PBXGroup;
			children = (
				8B50F6F926DF3AD80067759A /* VehicleModel.swift */,
				8B427DD926A6CD8500921A6D /* VehicleVC.swift */,
				8B7B1D3C27356CE1002D4094 /* Vehicle.storyboard */,
			);
			name = Vehicle;
			sourceTree = "<group>";
		};
		8B4400C1271432EF00126FD7 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				8B4400BB271432EF00126FD7 /* SOTabBar.modulemap */,
				8B4400BC271432EF00126FD7 /* SOTabBar-dummy.m */,
				8B4400BD271432EF00126FD7 /* SOTabBar-Info.plist */,
				8B4400BE271432EF00126FD7 /* SOTabBar-prefix.pch */,
				8B4400BF271432EF00126FD7 /* SOTabBar-umbrella.h */,
				8B4400C0271432EF00126FD7 /* SOTabBar.release.xcconfig */,
			);
			name = "Support Files";
			path = SOTabBar;
			sourceTree = "<group>";
		};
		8B4400C62714330200126FD7 /* LBX */ = {
			isa = PBXGroup;
			children = (
				8BB8C87526FF24B7007D5BC1 /* LBXPermissions.swift */,
				8BB8C87126FF24B4007D5BC1 /* LBXScanLineAnimation.swift */,
				8BB8C87026FF24B3007D5BC1 /* LBXScanNetAnimation.swift */,
				8BB8C87326FF24B6007D5BC1 /* LBXScanView.swift */,
				8BB8C87426FF24B6007D5BC1 /* LBXScanViewController.swift */,
				8BB8C87226FF24B5007D5BC1 /* LBXScanViewStyle.swift */,
				8BB8C87626FF24B8007D5BC1 /* LBXScanWrapper.swift */,
			);
			path = LBX;
			sourceTree = "<group>";
		};
		8B4400C72714331500126FD7 /* SOTab */ = {
			isa = PBXGroup;
			children = (
				8B4400BA271432EE00126FD7 /* SOTabBar.swift */,
				8B4400B9271432EE00126FD7 /* SOTabBarController.swift */,
				8B4400B7271432EC00126FD7 /* SOTabBarItem.swift */,
				8B4400B8271432ED00126FD7 /* SOTabBarSetting.swift */,
				8B4400C1271432EF00126FD7 /* Support Files */,
			);
			path = SOTab;
			sourceTree = "<group>";
		};
		8B6874D726CBC8FA00830A1F /* Complain */ = {
			isa = PBXGroup;
			children = (
				8BD18C6F26E1D69A00CABF3F /* ComplainModel.swift */,
				8B6874D826CBC91300830A1F /* AddComplainVC.swift */,
				8B6874DA26CBC93300830A1F /* ComplainListVC.swift */,
				8B6874DC26CBC95500830A1F /* ComplainCell.swift */,
				8B7B1D1E27356C54002D4094 /* Complain.storyboard */,
			);
			name = Complain;
			sourceTree = "<group>";
		};
		8B6874E026CBD2C600830A1F /* Language */ = {
			isa = PBXGroup;
			children = (
				8B6874E126CBD31200830A1F /* LanguageVC.swift */,
				8B7B1D2327356C5F002D4094 /* Language.storyboard */,
			);
			name = Language;
			sourceTree = "<group>";
		};
		8B79AFD126AC14D000A71D86 /* Charging */ = {
			isa = PBXGroup;
			children = (
				8B79AFD226AC14EC00A71D86 /* ChargingVC.swift */,
				8B287C1D26F46EF2006CAAE9 /* ScanQRVC.swift */,
				8B22E6332746521F008FB06B /* ConnectionLostVC.swift */,
				8B7B1D1927356C2D002D4094 /* Charging.storyboard */,
			);
			name = Charging;
			sourceTree = "<group>";
		};
		8B79AFD426AC168600A71D86 /* Wallet */ = {
			isa = PBXGroup;
			children = (
				8BD18C6D26E0E58900CABF3F /* WalletModel.swift */,
				8BCE825E270433240023D2EF /* TransactionModel.swift */,
				8B79AFD526AC169A00A71D86 /* WalletVC.swift */,
				8B6BEB9B26BCFB570033611B /* WalletCell.swift */,
				8B6BEB9D26BD34D20033611B /* HistoryVC.swift */,
				8B6874D526CA37C400830A1F /* AddMoneyVC.swift */,
				8B5F75B5272BF05500839D98 /* OfferCouponCell.swift */,
				8B5F75B7272C02B300839D98 /* TermsCell.swift */,
				8B162F4426CFB500000D20E2 /* PaymentDetailsVC.swift */,
				8B162F4626CFB526000D20E2 /* PaymentDetailsCell.swift */,
				8B80C793270330B800450242 /* TransactionDetailsVC.swift */,
				8BCE82602704341A0023D2EF /* TransactionCell.swift */,
				8BC054E12735447D00D8EC24 /* Wallet.storyboard */,
			);
			name = Wallet;
			sourceTree = "<group>";
		};
		8B79AFD926AC177600A71D86 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				8BC7D79C26C3D574004D1B8A /* Roboto-Black.ttf */,
				8BC7D79B26C3D573004D1B8A /* Roboto-BlackItalic.ttf */,
				8BC7D79726C3D56D004D1B8A /* Roboto-Bold.ttf */,
				8BC7D79926C3D570004D1B8A /* Roboto-BoldItalic.ttf */,
				8BC7D79E26C3D577004D1B8A /* Roboto-Italic.ttf */,
				8BC7D79D26C3D576004D1B8A /* Roboto-Light.ttf */,
				8BC7D79826C3D56F004D1B8A /* Roboto-LightItalic.ttf */,
				8BC7D7A026C3D57B004D1B8A /* Roboto-Medium.ttf */,
				8BC7D79A26C3D571004D1B8A /* Roboto-MediumItalic.ttf */,
				8BC7D79F26C3D579004D1B8A /* Roboto-Regular.ttf */,
				8BC7D79526C3D56B004D1B8A /* Roboto-Thin.ttf */,
				8BC7D79626C3D56C004D1B8A /* Roboto-ThinItalic.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		8B82761F26EB12EB002ED58C /* Icons */ = {
			isa = PBXGroup;
			children = (
				8BFB24B927116963002EE4B0 /* ic_map_marker.png */,
				8B82761D26EB12E5002ED58C /* ic_mapMarker.svg */,
				8B82762026EB144D002ED58C /* ic_marker_map.png */,
			);
			path = Icons;
			sourceTree = "<group>";
		};
		8BBA48BA26D617D8008554A4 /* Onboarding */ = {
			isa = PBXGroup;
			children = (
				8BBA48B126D617D1008554A4 /* ATCClassicWalkthroughViewController.swift */,
				8BBA48AC26D617CF008554A4 /* ATCClassicWalkthroughViewController.xib */,
				8BBA48AE26D617D0008554A4 /* ATCWalkthroughModel.swift */,
				8BBA48B026D617D0008554A4 /* ATCWalkthroughViewController.swift */,
				8BBA48AF26D617D0008554A4 /* ATCWalkthroughViewController.xib */,
				8BBA48AA26D617CE008554A4 /* UIColor.swift */,
				8BBA48AB26D617CF008554A4 /* UIImage.swift */,
				8BBA48AD26D617D0008554A4 /* UIViewController.swift */,
			);
			path = Onboarding;
			sourceTree = "<group>";
		};
		8BCE82682704684F0023D2EF /* About Us */ = {
			isa = PBXGroup;
			children = (
				8BFB24C327119F39002EE4B0 /* GameModel.swift */,
				8BCE8262270467B10023D2EF /* HelpVC.swift */,
				8BCE8266270467E10023D2EF /* AboutUsVC.swift */,
				8BFB24BD27119B1C002EE4B0 /* GameListVC.swift */,
				8BFB24BF27119B33002EE4B0 /* GameListCell.swift */,
				8BFB24C127119B47002EE4B0 /* GameDetailVC.swift */,
				8B2D578B2713DAE4007224D6 /* TermsVC.swift */,
				8B84F3E127218336008C8DA9 /* NewsVC.swift */,
				8B7F396D2742592700621296 /* BuyChargerVC.swift */,
				8B7B1D2D27356C76002D4094 /* Help.storyboard */,
			);
			name = "About Us";
			sourceTree = "<group>";
		};
		8BEB3CA326A7E007000BEA73 /* Home */ = {
			isa = PBXGroup;
			children = (
				8BEB3CAC26A91C26000BEA73 /* HomeTabVC.swift */,
				8B7B1D3727356CD6002D4094 /* Home.storyboard */,
			);
			name = Home;
			sourceTree = "<group>";
		};
		8BEB3D2926A94FBE000BEA73 /* Charger */ = {
			isa = PBXGroup;
			children = (
				8BE077A826E9CDC300D63131 /* ChargeStationModel.swift */,
				8BEB3D2A26A94FDB000BEA73 /* MapChargerVC.swift */,
				8BF0295726F054230026B21E /* ChargeStationSearchCell.swift */,
				8B3FFCF726D36FD700858D86 /* ConnectorCell.swift */,
				8B68752926CE1B2400830A1F /* MenuCell.swift */,
				8BEB3D2D26A94FE9000BEA73 /* ListChargerVC.swift */,
				8BEB3D3626A98844000BEA73 /* FilterVC.swift */,
				8B389B9726B3F565002DCBA6 /* FilterCell.swift */,
				8B3FFCF926D395D200858D86 /* ConnectorDetailsVC.swift */,
				8BBCE5542716A62700B8B68C /* ConnectorTimeCell.swift */,
				8B3FFCFB26D3967300858D86 /* ConnectorDetailsCell.swift */,
				8B7B1D3227356CBD002D4094 /* Charger.storyboard */,
			);
			name = Charger;
			sourceTree = "<group>";
		};
		8BF1C7B426DE24610092358D /* Service */ = {
			isa = PBXGroup;
			children = (
				8B50F6F726DE24D10067759A /* AFWrapper.swift */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		E8C72C8D8D220CB2D247389B /* Pods */ = {
			isa = PBXGroup;
			children = (
				649DAB7331AB254FC54FA0F5 /* Pods-NXC EV Solutions.debug - live.xcconfig */,
				D3B3DAC8297597BA646000B1 /* Pods-NXC EV Solutions.debug - local.xcconfig */,
				7B470C53F29B44A42153B57F /* Pods-NXC EV Solutions.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8B427BA126A0491E00921A6D /* NXC EV Solutions */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8B427BB626A0492100921A6D /* Build configuration list for PBXNativeTarget "NXC EV Solutions" */;
			buildPhases = (
				7B07EFCBAA00D9147EA4FD0A /* [CP] Check Pods Manifest.lock */,
				8B427B9E26A0491E00921A6D /* Sources */,
				8B427B9F26A0491E00921A6D /* Frameworks */,
				8B427BA026A0491E00921A6D /* Resources */,
				C1998BC46D5A59FB44503783 /* [CP] Embed Pods Frameworks */,
				7BD5751930AC1DB33C6A2FDD /* [CP] Copy Pods Resources */,
				8B38F1032715B7FE0042C68D /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "NXC EV Solutions";
			productName = "NXC EV Solutions";
			productReference = 8B427BA226A0491E00921A6D /* NXC EV Solutions.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8B427B9A26A0491E00921A6D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1230;
				LastUpgradeCheck = 1230;
				TargetAttributes = {
					8B427BA126A0491E00921A6D = {
						CreatedOnToolsVersion = 12.3;
					};
				};
			};
			buildConfigurationList = 8B427B9D26A0491E00921A6D /* Build configuration list for PBXProject "NXC EV Solutions" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				hi,
				gu,
			);
			mainGroup = 8B427B9926A0491E00921A6D;
			productRefGroup = 8B427BA326A0491E00921A6D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8B427BA126A0491E00921A6D /* NXC EV Solutions */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8B427BA026A0491E00921A6D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8BC7D7A526C3D57B004D1B8A /* Roboto-BoldItalic.ttf in Resources */,
				8BC054DF2735447D00D8EC24 /* Wallet.storyboard in Resources */,
				8B7B1D3027356CBD002D4094 /* Charger.storyboard in Resources */,
				8BEB3D3426A972A4000BEA73 /* mapStyle.json in Resources */,
				8B7B1D3F27356CEE002D4094 /* Profile.storyboard in Resources */,
				8B7B1D3A27356CE1002D4094 /* Vehicle.storyboard in Resources */,
				8B548F8626E77F3100EB7964 /* LaunchScreen.storyboard in Resources */,
				8BC7D7A426C3D57B004D1B8A /* Roboto-LightItalic.ttf in Resources */,
				8BC7D7A326C3D57B004D1B8A /* Roboto-Bold.ttf in Resources */,
				8B7B1D3527356CD6002D4094 /* Home.storyboard in Resources */,
				8B7B1D1C27356C54002D4094 /* Complain.storyboard in Resources */,
				8BC7D7A826C3D57B004D1B8A /* Roboto-Black.ttf in Resources */,
				8BBA48B726D617D1008554A4 /* ATCWalkthroughViewController.xib in Resources */,
				8BC7D7AC26C3D57B004D1B8A /* Roboto-Medium.ttf in Resources */,
				8B82762126EB144E002ED58C /* ic_marker_map.png in Resources */,
				8BBA48B426D617D1008554A4 /* ATCClassicWalkthroughViewController.xib in Resources */,
				8B7B1D2127356C5F002D4094 /* Language.storyboard in Resources */,
				8BC7D7AA26C3D57B004D1B8A /* Roboto-Italic.ttf in Resources */,
				8B5F75BD272F9B1400839D98 /* Localizable.strings in Resources */,
				8B5F75B0272AA48B00839D98 /* GoogleService-Info.plist in Resources */,
				8BC7D7A726C3D57B004D1B8A /* Roboto-BlackItalic.ttf in Resources */,
				8BFB24BA27116963002EE4B0 /* ic_map_marker.png in Resources */,
				8BC7D7A926C3D57B004D1B8A /* Roboto-Light.ttf in Resources */,
				8BBA48BE26D61ACD008554A4 /* Assets.xcassets in Resources */,
				8BC7D7A226C3D57B004D1B8A /* Roboto-ThinItalic.ttf in Resources */,
				8BC7D7AB26C3D57B004D1B8A /* Roboto-Regular.ttf in Resources */,
				8B7B1D2B27356C76002D4094 /* Help.storyboard in Resources */,
				8B5F75C4272F9BA700839D98 /* PreLogin.storyboard in Resources */,
				8B548F8426E77F2700EB7964 /* Main.storyboard in Resources */,
				8B7B1D1727356C2D002D4094 /* Charging.storyboard in Resources */,
				8B82761E26EB12E6002ED58C /* ic_mapMarker.svg in Resources */,
				8BC7D7A126C3D57B004D1B8A /* Roboto-Thin.ttf in Resources */,
				8BC7D7A626C3D57B004D1B8A /* Roboto-MediumItalic.ttf in Resources */,
				8B7B1D2627356C6D002D4094 /* RFID.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		7B07EFCBAA00D9147EA4FD0A /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NXC EV Solutions-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7BD5751930AC1DB33C6A2FDD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8B38F1032715B7FE0042C68D /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		C1998BC46D5A59FB44503783 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-NXC EV Solutions/Pods-NXC EV Solutions-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8B427B9E26A0491E00921A6D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8B427CEA26A1831D00921A6D /* Constants.swift in Sources */,
				8BB8C87D26FF24B8007D5BC1 /* LBXPermissions.swift in Sources */,
				8BEA651926FC89A000131053 /* TrackRFIDStatusVC.swift in Sources */,
				8B162F4526CFB500000D20E2 /* PaymentDetailsVC.swift in Sources */,
				8B5F75B6272BF05500839D98 /* OfferCouponCell.swift in Sources */,
				8BBA48B526D617D1008554A4 /* UIViewController.swift in Sources */,
				8B389B9826B3F565002DCBA6 /* FilterCell.swift in Sources */,
				8BBA48B826D617D1008554A4 /* ATCWalkthroughViewController.swift in Sources */,
				8BB8C87F26FF24B8007D5BC1 /* BundleExtension.swift in Sources */,
				8BBA48C226D8AFFF008554A4 /* AddressVC.swift in Sources */,
				8BFB24C82711B16C002EE4B0 /* RearViewController.swift in Sources */,
				8B6874D626CA37C400830A1F /* AddMoneyVC.swift in Sources */,
				8B22E6342746521F008FB06B /* ConnectionLostVC.swift in Sources */,
				8B427C0C26A04F8500921A6D /* AppStoryboards.swift in Sources */,
				8BBCE5552716A62700B8B68C /* ConnectorTimeCell.swift in Sources */,
				8BFB24C227119B47002EE4B0 /* GameDetailVC.swift in Sources */,
				8BFB24C027119B33002EE4B0 /* GameListCell.swift in Sources */,
				8B4400C2271432EF00126FD7 /* SOTabBar.swift in Sources */,
				8B6BEB9C26BCFB570033611B /* WalletCell.swift in Sources */,
				8B84F3E227218336008C8DA9 /* NewsVC.swift in Sources */,
				8BCE825F270433240023D2EF /* TransactionModel.swift in Sources */,
				8BCE82612704341A0023D2EF /* TransactionCell.swift in Sources */,
				8BBA48B326D617D1008554A4 /* UIImage.swift in Sources */,
				8B427D5326A1831D00921A6D /* Extensions.swift in Sources */,
				8BFB24C72711B16C002EE4B0 /* ViewController.swift in Sources */,
				8B2D578C2713DAE4007224D6 /* TermsVC.swift in Sources */,
				8B287C1E26F46EF2006CAAE9 /* ScanQRVC.swift in Sources */,
				8B427BA626A0491E00921A6D /* AppDelegate.swift in Sources */,
				8BD18C7026E1D69A00CABF3F /* ComplainModel.swift in Sources */,
				8B79AFD626AC169A00A71D86 /* WalletVC.swift in Sources */,
				8BEA651326FC7B7200131053 /* RFIDTrackListVC.swift in Sources */,
				8B6874E226CBD31200830A1F /* LanguageVC.swift in Sources */,
				8B68752E26CF88E300830A1F /* ProfileDetailsVC.swift in Sources */,
				8BBA48C426D8BF35008554A4 /* ProfileModel.swift in Sources */,
				8B162F4726CFB526000D20E2 /* PaymentDetailsCell.swift in Sources */,
				8BFB24C427119F39002EE4B0 /* GameModel.swift in Sources */,
				8BBA48B926D617D1008554A4 /* ATCClassicWalkthroughViewController.swift in Sources */,
				8B6BEB9E26BD34D20033611B /* HistoryVC.swift in Sources */,
				8BBA48C026D61B78008554A4 /* OnboardingVC.swift in Sources */,
				8BE077A926E9CDC400D63131 /* ChargeStationModel.swift in Sources */,
				8BCE8263270467B10023D2EF /* HelpVC.swift in Sources */,
				8BEA651B26FC912600131053 /* IssueNewCardVC.swift in Sources */,
				8B427DA526A194E100921A6D /* VerificationVC.swift in Sources */,
				8B3FFCFC26D3967300858D86 /* ConnectorDetailsCell.swift in Sources */,
				8B427C1B26A13C9900921A6D /* LoginVC.swift in Sources */,
				8B50F6F826DE24D10067759A /* AFWrapper.swift in Sources */,
				8B4400C5271432EF00126FD7 /* SOTabBarItem.swift in Sources */,
				8BB8C86F26FF1451007D5BC1 /* Configuration.swift in Sources */,
				8B5EA6C326FDBC6C00033C87 /* ReplaceCardVC.swift in Sources */,
				8BB8C87A26FF24B8007D5BC1 /* LBXScanViewStyle.swift in Sources */,
				8BFB24BE27119B1C002EE4B0 /* GameListVC.swift in Sources */,
				8BB8C87926FF24B8007D5BC1 /* LBXScanLineAnimation.swift in Sources */,
				8B84F3E0271FD590008C8DA9 /* TrackRFIDStatusCell.swift in Sources */,
				8BEA651726FC7FE100131053 /* RFIDModel.swift in Sources */,
				8BF0295826F054230026B21E /* ChargeStationSearchCell.swift in Sources */,
				8BBA48B626D617D1008554A4 /* ATCWalkthroughModel.swift in Sources */,
				8BD18C6E26E0E58900CABF3F /* WalletModel.swift in Sources */,
				8BCE8267270467E10023D2EF /* AboutUsVC.swift in Sources */,
				8B3FFCF826D36FD700858D86 /* ConnectorCell.swift in Sources */,
				8B68752C26CF88CA00830A1F /* ProfileDetailsCell.swift in Sources */,
				8BEB3D3726A98844000BEA73 /* FilterVC.swift in Sources */,
				8B4400C4271432EF00126FD7 /* SOTabBarController.swift in Sources */,
				8B427DDA26A6CD8500921A6D /* VehicleVC.swift in Sources */,
				8B80C794270330B800450242 /* TransactionDetailsVC.swift in Sources */,
				8BB8C87826FF24B8007D5BC1 /* LBXScanNetAnimation.swift in Sources */,
				8B68752A26CE1B2400830A1F /* MenuCell.swift in Sources */,
				8BEB3D2E26A94FE9000BEA73 /* ListChargerVC.swift in Sources */,
				8B79AFD326AC14EC00A71D86 /* ChargingVC.swift in Sources */,
				8BB8C87B26FF24B8007D5BC1 /* LBXScanView.swift in Sources */,
				8BBA48B226D617D1008554A4 /* UIColor.swift in Sources */,
				8B6874D926CBC91300830A1F /* AddComplainVC.swift in Sources */,
				8BEB3CAD26A91C26000BEA73 /* HomeTabVC.swift in Sources */,
				8B427C1826A13C8600921A6D /* SplashVC.swift in Sources */,
				8B4400C92714431200126FD7 /* MenuVC.swift in Sources */,
				8B6874DD26CBC95500830A1F /* ComplainCell.swift in Sources */,
				8B3FFD0326D4C08800858D86 /* ViewCardVC.swift in Sources */,
				8BB8C87C26FF24B8007D5BC1 /* LBXScanViewController.swift in Sources */,
				8B50F6FA26DF3AD80067759A /* VehicleModel.swift in Sources */,
				8B3FFD0126D4C06E00858D86 /* OrderCardVC.swift in Sources */,
				8B427DAC26A19E7700921A6D /* UserInfoVC.swift in Sources */,
				8B4400C3271432EF00126FD7 /* SOTabBarSetting.swift in Sources */,
				8BB8C87E26FF24B8007D5BC1 /* LBXScanWrapper.swift in Sources */,
				8B7F396E2742592700621296 /* BuyChargerVC.swift in Sources */,
				8B3FFCFA26D395D200858D86 /* ConnectorDetailsVC.swift in Sources */,
				8BEA651526FC7BD900131053 /* RFIDTrackListCell.swift in Sources */,
				8B6874DB26CBC93300830A1F /* ComplainListVC.swift in Sources */,
				8BEB3D2B26A94FDB000BEA73 /* MapChargerVC.swift in Sources */,
				8B5F75B8272C02B400839D98 /* TermsCell.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		8B5F75BF272F9B1400839D98 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				8B5F75C1272F9B1F00839D98 /* en */,
				8B29460D273A59830047FF6D /* hi */,
				8B4D8361273A962F009C0CA9 /* gu */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		8B5F75C6272F9BA700839D98 /* PreLogin.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8BC8999A27350CFE00151B02 /* Base */,
				8B294602273A59500047FF6D /* hi */,
				8B4D8356273A95F5009C0CA9 /* gu */,
			);
			name = PreLogin.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D1927356C2D002D4094 /* Charging.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D1827356C2D002D4094 /* Base */,
				8B294607273A59530047FF6D /* hi */,
				8B4D835B273A95FA009C0CA9 /* gu */,
			);
			name = Charging.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D1E27356C54002D4094 /* Complain.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D1D27356C54002D4094 /* Base */,
				8B294609273A59810047FF6D /* hi */,
				8B4D835D273A962D009C0CA9 /* gu */,
			);
			name = Complain.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D2327356C5F002D4094 /* Language.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D2227356C5F002D4094 /* Base */,
				8B29460A273A59820047FF6D /* hi */,
				8B4D835E273A962D009C0CA9 /* gu */,
			);
			name = Language.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D2827356C6D002D4094 /* RFID.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D2727356C6D002D4094 /* Base */,
				8B29460B273A59830047FF6D /* hi */,
				8B4D835F273A962E009C0CA9 /* gu */,
			);
			name = RFID.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D2D27356C76002D4094 /* Help.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D2C27356C76002D4094 /* Base */,
				8B29460C273A59830047FF6D /* hi */,
				8B4D8360273A962E009C0CA9 /* gu */,
			);
			name = Help.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D3227356CBD002D4094 /* Charger.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D3127356CBD002D4094 /* Base */,
				8B294606273A59530047FF6D /* hi */,
				8B4D835A273A95F9009C0CA9 /* gu */,
			);
			name = Charger.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D3727356CD6002D4094 /* Home.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D3627356CD6002D4094 /* Base */,
				8B294605273A59520047FF6D /* hi */,
				8B4D8359273A95F8009C0CA9 /* gu */,
			);
			name = Home.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D3C27356CE1002D4094 /* Vehicle.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D3B27356CE1002D4094 /* Base */,
				8B294604273A59510047FF6D /* hi */,
				8B4D8358273A95F7009C0CA9 /* gu */,
			);
			name = Vehicle.storyboard;
			sourceTree = "<group>";
		};
		8B7B1D4127356CEE002D4094 /* Profile.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8B7B1D4027356CEE002D4094 /* Base */,
				8B294603273A59500047FF6D /* hi */,
				8B4D8357273A95F7009C0CA9 /* gu */,
			);
			name = Profile.storyboard;
			sourceTree = "<group>";
		};
		8BC054E12735447D00D8EC24 /* Wallet.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8BC054E02735447D00D8EC24 /* Base */,
				8B294608273A59540047FF6D /* hi */,
				8B4D835C273A95FA009C0CA9 /* gu */,
			);
			name = Wallet.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8B2C3D8326AE664C00E86D9E /* Debug - Local */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = "Debug - Local";
		};
		8B2C3D8426AE664C00E86D9E /* Debug - Local */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D3B3DAC8297597BA646000B1 /* Pods-NXC EV Solutions.debug - local.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BASE_URL = "https://staging.cocoacasts.com";
				CODE_SIGN_ENTITLEMENTS = "NXC EV Solutions/NXC EV SolutionsDebug - Local.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				INFOPLIST_FILE = "NXC EV Solutions/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.9;
				OTHER_SWIFT_FLAGS = "$(inherited) -D DEBUG -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.nxccontrols.nxcev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug - Local";
		};
		8B427BB426A0492100921A6D /* Debug - Live */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.3;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = "Debug - Live";
		};
		8B427BB526A0492100921A6D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8B427BB726A0492100921A6D /* Debug - Live */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 649DAB7331AB254FC54FA0F5 /* Pods-NXC EV Solutions.debug - live.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BASE_URL = "https://cocoacasts.com";
				CODE_SIGN_ENTITLEMENTS = "NXC EV Solutions/NXC EV Solutions.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				INFOPLIST_FILE = "NXC EV Solutions/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.9;
				OTHER_SWIFT_FLAGS = "$(inherited) -D DEBUG -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.nxccontrols.nxcev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug - Live";
		};
		8B427BB826A0492100921A6D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7B470C53F29B44A42153B57F /* Pods-NXC EV Solutions.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				BASE_URL = "https://staging.cocoacasts.com";
				CODE_SIGN_ENTITLEMENTS = "NXC EV Solutions/NXC EV SolutionsRelease.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGRSTV929R;
				INFOPLIST_FILE = "NXC EV Solutions/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.9;
				OTHER_SWIFT_FLAGS = "$(inherited) -D DEBUG -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.nxccontrols.nxcev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8B427B9D26A0491E00921A6D /* Build configuration list for PBXProject "NXC EV Solutions" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8B427BB426A0492100921A6D /* Debug - Live */,
				8B2C3D8326AE664C00E86D9E /* Debug - Local */,
				8B427BB526A0492100921A6D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug - Local";
		};
		8B427BB626A0492100921A6D /* Build configuration list for PBXNativeTarget "NXC EV Solutions" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8B427BB726A0492100921A6D /* Debug - Live */,
				8B2C3D8426AE664C00E86D9E /* Debug - Local */,
				8B427BB826A0492100921A6D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug - Local";
		};
/* End XCConfigurationList section */
	};
	rootObject = 8B427B9A26A0491E00921A6D /* Project object */;
}
