//
//  Constants.swift
//  iCharge2021
//
//  Created by <PERSON><PERSON><PERSON> on 08/04/21.
//

import Foundation
import UIKit


//enum Configuration: String {
//
//    // MARK: - Configurations
//    case debugLive
//    case debugLocal
//    case release
//
//
//    // MARK: - Current Configuration
//    static let current: Configuration = {
//        guard let rawValue = Bundle.main.infoDictionary?["Configuration"] as? String else {
//            fatalError("No Configuration Found")
//        }
//
//        guard let configuration = Configuration(rawValue: rawValue.lowercased()) else {
//            fatalError("Invalid Configuration")
//        }
//
//        return configuration
//    }()
//
//    // MARK: - Base URL
//    static var baseURL: URL {
////        let current: Configuration = {
////            guard let rawValue = Bundle.main.infoDictionary?["Configuration"] as? String else {
////                fatalError("No Configuration Found")
////            }
////        }
//
//        let current: Configuration =  Bundle.main.infoDictionary!["Configuration"] as! String
//        switch current {
//        case .debugLocal:
//            print("https://staging.cocoacasts.com")
//            return URL(string: "https://staging.cocoacasts.com")!
//        case .debugLive, .release:
//            print("https://cocoacasts.com")
//            return URL(string: "https://cocoacasts.com")!
//        }
//    }
//}

struct Constants {

    static let AppName                      = "iCharge"

    static let Map_Key                      = "AIzaSyCl3-ndILPjcRspk_CELd8NnZLP2Kf5gJQ"
    static let Places_Key                   = "AIzaSyCl3-ndILPjcRspk_CELd8NnZLP2Kf5gJQ"

    var baseURL = ""

//    //DEBUG - LIVE
//    static let BASE_URL                     = "http://ip.nxc.co.in:8080/charge_rfid/Api_v2/"
//    static let WEBSOCKET_URL                = "ws://ip.nxc.co.in:8888"
//
//    //DEBUG - LOCAL
   static let BASE_URL                     = "http://*************/evcs-laravel/public/api/"
   static let WEBSOCKET_URL                = "ws://*************:20087"

    //RELEASE
   //  static let BASE_URL                     = "https://evcs.nxccontrols.in/api/"
   //  static let WEBSOCKET_URL                = "ws://evcs.nxccontrols.in:20087"



    //Charger Screen Server URL Port
    //8886



//    static let Map_Key                      = "AIzaSyCl3-ndILPjcRspk_CELd8NnZLP2Kf5gJQ"
//    static let Places_Key                   = "AIzaSyCl3-ndILPjcRspk_CELd8NnZLP2Kf5gJQ"
//    static let API_Key                      = "AIzaSyBT9ZSVeX5CtLyIYgPBQvyQ7xjNuieMO3Y"

    static let primaryColor                 = UIColor(hexString: "047E6D")
    static let menuTextColor                = UIColor(hexString: "272424")
    static let menuIconColor                = UIColor(hexString: "999999")

    static let accentColor                  = UIColor(hexString: "9E0C21")

    static let textBorderColor              = UIColor(hexString: "B9BAC8")
    static let textLightColor               = UIColor(hexString: "4A4A4A")
    static let textNotSelectedColor         = UIColor(hexString: "999999")
    static let backArrowBorderColor         = UIColor(hexString: "dddddd")

    static let languageDefaultColor         = UIColor(hexString: "EFEFEF")
    static let secondaryGrayText            = UIColor(hexString: "5D5D5D")
    static let inProgressColor              = UIColor(hexString: "27B1EC")
    static let userProfileBG                = UIColor(hexString: "F9F9F9")
    static let partitionBG                  = UIColor(hexString: "E9E9E9")
    static let creditColor                  = UIColor(hexString: "3B7CFF")

    static let paymentSuccessColor          = UIColor(hexString: "2ABA00")

    static let timeSelectBgColor            = UIColor(hexString: "495062")
    static let timeSelectDotColor           = UIColor(hexString: "F28251")

    static let orangeTextColor              = UIColor(hexString: "FE7A2C")

    static let IS_TXN_STOPPED               = "isTxnStopped"
    static let IS_CHARGING                  = "isCharging"

    static let IS_NDCP                      = "isNDCP"
    static let IS_ONBOARDING                = "isBoarding"
    static let IS_LOGIN                     = "isLogin"
    static let IS_OTP                       = "isOtp"
    static let IS_PASSCODE                  = "isPasscode"
    static let PIN                          = "pin"
    static let USER_PROFILE                 = "user_profile"

    static let VERIFICATION_ID              = "verificationID"
    static let FIREBASE_UID                 = "firebase_uid"

    static let DEVICE_TOKEN                 = "deviceToken"
    static let USER_OTP                     = "UserOtp"

    static let USER_PROFILE_DETAILS         = "user_profile_details"

    static let USER_ID                      = "user_id"
    static let PROFILE_EDIT                 = "profile_edit"
    static let VEHICLE_EDIT                 = "vehicle_edit"
    static let JWT_TOKEN                    = "jwt_token"

    static let USER_EMAIL                   = "user_email"
    static let FIRST_NAME                   = "first_name"
    static let LAST_NAME                    = "last_name"
    static let BIRTH_DAY                    = "birth_day"
    static let SEX                          = "sex"
    static let PHONE                        = "phone"
    static let BALANCE                      = "balance"
    static let HOUSE_NUMBER                 = "house_number"
    static let STREET_NAME                  = "street_name"
    static let PINCODE                      = "pincode"
    static let CITY                         = "city"
    static let COUNTRY                      = "country"
    static let OCPP_TAG_PK                  = "ocpp_tag_pk"
    static let OCPP_BLOCKED                 = "ocpp_blocked"
    static let OCPP_ID_TAG                  = "ocpp_id_tag"
    static let OCPP_IN_TRANSACTION          = "ocpp_in_transaction"
    static let MOBILE_PIN                   = "mobile_pin"

//    static let START_URL                    = "start_url"
//    static let MAIN_URL                     = "main_url"
//    static let END_URL                      = "end_url"

    static let MERCHANT_KEY                 = "KaMhPdXC"
    static let SALT_KEY                     = "KCp9QagMDB"

    static let RELOAD_DETAILS               = "reload_Details"
    static let STR_SHOWMETER                = "strShowMeter"

    static let SCAN_QRCODE                  = "scan_qrcode"

    static let VEHICLE_ID                   = "vehicle_id"
    static let VEHICLE_NAME                 = "vehicle_name"

    static let FCM_TOKEN                    = "fcm_token"

    static let LATITUDE                     = "latitude"
    static let LONGITUDE                    = "longitude"

    static let ISCHARGING_DROPDOWN_CENTER   = "isChargingDropdownCenter"

    static let LANGUAGE                      =  "language"
}

struct Colors {
    static let ACCENT                       = "Accent"
    static let PRIMARY                      = "Primary"
    static let PRIMARY_DARK_BG              = "PrimaryDarkBG"
    static let PRIMARY_DARK_TEXT            = "PrimaryDarkText"
    static let PRIMARY_SELECTION            = "PrimarySelection"
    static let PRIMARY_TEXT                 = "PrimaryText"
    static let PRIMARY_TEXT_LIGHT           = "PrimaryTextLight"
    static let SPLASH                       = "Splash"
    static let WALLET_ADD_TEXT              = "WalletAddText"
    static let WALLET_DEDUCT_TEXT           = "WalletDeductText"
}


struct API {

    static let LOGIN                        = "login"
    static let CHECK_OTP                    = "check_otp"
    static let GET_COUNTRY                  = "get_country"
    static let GET_STATE                    = "get_state"
    static let GET_CITY                     = "get_city"
    static let GET_GENDER                   = "get_gender"
    static let VIEW_PROFILE                 = "view_profile"
    static let EDIT_PROFILE                 = "edit_profile"
    static let GET_VEHICLE_BRAND            = "get_vehicle_brand"
    static let GET_VEHICLE_TYPE             = "get_vehicle_type"
    static let GET_VEHICLE_MODEL            = "get_vehicle_model"
    static let CAR_REGISTRATION             = "car_registration"
    static let FAQ_LIST                     = "faq_list"
    static let FAQ_DETAILS                  = "faq_details"
    static let GET_WALLET_HISTORY           = "get_wallet_history"
    static let NEAR_ME_LIST                 = "near_me_list"
    static let FAV_CHARGE_LIST              = "fav_charge_list"
    static let CS_DETAILS                   = "cs_details"
    static let CS_INFO                      = "charge_station_info"
    static let ADD_FAV_CHARGING_STATION     = "add_favourite_charging_station"
    static let CONNECTER_DETAIL             = "connecter_detail"
    static let CONNECTER_TYPE               = "connecter_type"
    static let GENERATE_TXN_ID              = "generate_txn_id"
    static let GET_HASH_DATA                = "get_hash_data"
    static let GET_TRANSACTION_DETAILS      = "get_transaction_details"
    static let GET_TRANSACTION_INFO         = "get_transcation_info"
    static let PAYMENT_DETAILS              = "payment_details"
    static let GET_TRANSACTION_VEHICLE      = "get_transaction_vehicle"
    static let ADD_RATE                     = "add_rate"
    static let BILLDESK_RESPONSE            = "billdesk_response"
    static let BILLDESK_MESSAGE_RESPONSE    = "billdesk_message_response"
    static let GET_HELP_TYPE                = "get_help_type"
    static let GET_PREVIOUS_CS_TRANSACTION  = "get_previous_cs_transaction"

    static let GET_USER_COMPLAIN_TYPE       = "get_user_complain_type"
    static let ADD_USER_COMPLAIN            = "add_user_complain"

    static let GET_LAST_TRANSACTION         = "get_last_transaction"
    static let USER_COMPLAIN_LIST           = "user_complain_list"

    static let RESEND_USER_ACTIVATE         = "resend_user_activate"

    static let ADD_USER_WALLET              = "add_user_wallet"
    static let PAYMENT_METHOD               = "payment_method"
    static let INITIATE_PAYMENT             = "initiate_payment"

    static let SEARCH_STATION               = "search_station"

    static let ORDER_RFID                   = "order_rfid"
    static let TRACE_RFID                   = "trace_rfid"
    static let VIEW_RFID_ORDER              = "view_rfid_order"
    static let RFID_LIST                    = "rfid_list"
    static let REPLACE_RFID                 = "replace_rfid"
    static let BLOCK_RFID                   = "block_card"

    static let WALLET_BALANCE               = "wallet_balance"

    static let ABOUT_US                     = "get_about_us"
    static let HELP                         = "get_help_us"

    static let DELETE_VEHICLE               = "delete_vehicle"
    static let EDIT_VEHICLE                 = "edit_vehicle"

    static let GAME_LIST                    = "game_list"
    static let NEWS_URL                     = "news_url"
    static let BUY_CHARGER                  = "buy_charger"

    static let COMPLAIN_FILTER_TYPE         = "complain_filter_type"

}

struct WebSocketDetails {

    static let EVENT_BROADCAST                  = "server_event_broadcast"
    static let EVENT                            = "event"
    static let EVENT_USER_ID                    = "user_id"
    static let EVENT_USER_MOBILE                = "user_mobile"
    static let EVENT_PAYLOAD                    = "payload"
    static let EVENT_SCANQR                     = "scanedqr_start"
    static let EVENT_HANDSHAKE                  = "handshake"
    static let EVENT_USER_PROFILE               = "user_profile"
    static let EVENT_SHOW_METER_VALUE           = "show_meter_value"
    static let EVENT_TRANSACTION_START          = "transaction_start_event"
    static let EVENT_TRANSACTION_STOPPED        = "transaction_stoped_event"
    static let EVENT_SET_PIN                    = "set_pin"
    static let EVENT_GET_BALANCE                = "get_balance"
    static let EVENT_GET_HISTORY                = "get_history"
    static let EVENT_SMS_OTP                    = "sms_otp"
    static let EVENT_SMS_VERIFY_OTP             = "sms_verify_otp"
    static let EVENT_INSUFFICIENT_BALANCE       = "insufficient_balance"

    static let EVENT_REMOTE_START_TRANSACTION   = "MobileRemoteStartTransactionRequest"
    static let EVENT_REMOTE_STOP_TRANSACTION    = "MobileRemoteStopTransactionRequest"

}

struct NotificationName {
    static let RELOAD_NOTIFY = NSNotification.Name("ReloadNotify")
}


